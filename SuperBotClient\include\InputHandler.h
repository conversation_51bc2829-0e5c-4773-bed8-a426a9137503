﻿#pragma once

#include "Common.h"

class InputHandler
{
public:
    InputHandler();
    ~InputHandler();

    // Initialization
    bool initialize();
    void shutdown();

    // Input control
    void setInputEnabled(bool enabled) { m_inputEnabled = enabled; }
    bool isInputEnabled() const { return m_inputEnabled; }

    // Event processing
    void processEvents();

    // Mouse control
    void handleMouseMove(const Point& position);
    void handleMouseClick(const Point& position, MouseButton button, bool pressed);
    void handleMouseScroll(const Point& position, int delta);
    void setMousePosition(const Point& position);
    Point getMousePosition() const;

    // Keyboard control
    void handleKeyPress(uint32_t keyCode, uint32_t modifiers);
    void handleKeyRelease(uint32_t keyCode, uint32_t modifiers);
    void handleTextInput(const std::string& text);
    void sendKeySequence(const std::vector<uint32_t>& keyCodes);

    // Input validation and security
    void setInputFiltering(bool enabled) { m_inputFiltering = enabled; }
    void addBlockedKey(uint32_t keyCode);
    void removeBlockedKey(uint32_t keyCode);
    void setAllowedInputRegion(const Rect& region);
    void clearAllowedInputRegion();

    // Input simulation settings
    void setInputDelay(int milliseconds) { m_inputDelay = milliseconds; }
    void setMouseAcceleration(bool enabled) { m_mouseAcceleration = enabled; }
    void setKeyRepeatEnabled(bool enabled) { m_keyRepeatEnabled = enabled; }
    void setKeyRepeatDelay(int milliseconds) { m_keyRepeatDelay = milliseconds; }
    void setKeyRepeatRate(int rate) { m_keyRepeatRate = rate; }

    // Clipboard integration
    void setClipboardText(const std::string& text);
    std::string getClipboardText() const;
    void setClipboardImage(const std::vector<uint8_t>& imageData);
    std::vector<uint8_t> getClipboardImage() const;

    // Input state
    bool isKeyPressed(uint32_t keyCode) const;
    bool isMouseButtonPressed(MouseButton button) const;
    uint32_t getCurrentModifiers() const;

    // Statistics
    int getInputEventsProcessed() const { return m_inputEventsProcessed; }
    int getInputEventsBlocked() const { return m_inputEventsBlocked; }
    void resetStatistics();

    // Event handlers
    void processMouseEvent(const MouseEvent& event);
    void processKeyboardEvent(const KeyboardEvent& event);

    // Callbacks
    void setInputProcessedCallback(std::function<void(const std::string&)> callback);
    void setInputBlockedCallback(std::function<void(const std::string&)> callback);
    void setInputErrorCallback(std::function<void(const std::string&)> callback);

private:
    // Platform-specific input methods
    bool initializePlatformInput();
    void cleanupPlatformInput();
    bool simulateMouseMove(const Point& position);
    bool simulateMouseClick(const Point& position, MouseButton button, bool pressed);
    bool simulateMouseScroll(const Point& position, int delta);
    bool simulateKeyPress(uint32_t keyCode, bool pressed);
    bool simulateTextInput(const std::string& text);

    // Input validation
    bool validateMouseInput(const Point& position);
    bool validateKeyInput(uint32_t keyCode);
    bool isKeyBlocked(uint32_t keyCode) const;
    bool isPositionAllowed(const Point& position) const;

    // Input queue management
    void queueInputEvent(const std::function<bool()>& inputFunction);
    void processInputQueue();
    void clearInputQueue();

    // Key state management
    void updateKeyState(uint32_t keyCode, bool pressed);
    void updateMouseButtonState(MouseButton button, bool pressed);
    void updateModifierState(uint32_t modifiers);

    // Clipboard management
    void updateClipboard();
    void handleClipboardChange();

    // Security and filtering
    bool checkInputSecurity(const std::string& operation);
    void logInputEvent(const std::string& event, bool blocked = false);

    // Input settings
    bool m_inputEnabled;
    bool m_inputFiltering;
    bool m_mouseAcceleration;
    bool m_keyRepeatEnabled;
    int m_inputDelay;
    int m_keyRepeatDelay;
    int m_keyRepeatRate;

    // Input regions and restrictions
    Rect m_allowedInputRegion;
    bool m_useInputRegion;
    std::set<uint32_t> m_blockedKeys;

    // Input state tracking
    std::set<uint32_t> m_pressedKeys;
    std::set<MouseButton> m_pressedMouseButtons;
    uint32_t m_currentModifiers;
    Point m_lastMousePosition;

    // Input queue and timing
    std::queue<std::function<bool()>> m_inputQueue;
    std::chrono::steady_clock::time_point m_lastInputTime;
    std::chrono::steady_clock::time_point m_lastKeyRepeatTime;
    uint32_t m_lastRepeatedKey;

    // Clipboard state
    std::string m_lastClipboardText;
    std::vector<uint8_t> m_lastClipboardImage;

    // Statistics
    int m_inputEventsProcessed;
    int m_inputEventsBlocked;

    // Callbacks
    std::function<void(const std::string&)> m_inputProcessedCallback;
    std::function<void(const std::string&)> m_inputBlockedCallback;
    std::function<void(const std::string&)> m_inputErrorCallback;

    // Windows-specific data
    struct WindowsInputData;
    std::unique_ptr<WindowsInputData> m_windowsData;

    // Constants
    static constexpr int DEFAULT_INPUT_DELAY = 1;  // milliseconds
    static constexpr int DEFAULT_KEY_REPEAT_DELAY = 500;  // milliseconds
    static constexpr int DEFAULT_KEY_REPEAT_RATE = 30;  // keys per second
    static constexpr int INPUT_QUEUE_MAX_SIZE = 1000;
};
