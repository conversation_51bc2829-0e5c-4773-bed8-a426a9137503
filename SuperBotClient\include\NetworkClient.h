﻿#pragma once

#include "Common.h"

class NetworkClient
{
public:
    NetworkClient();
    ~NetworkClient();

    // Initialization
    bool initialize(int port);
    void shutdown();

    // Server management
    bool startServer();
    void stopServer();
    bool isListening() const;
    int serverPort() const;

    // Connection management
    bool hasActiveConnection() const;
    std::string clientAddress() const;
    void disconnectClient();

    // Message handling
    void sendMessage(MessageType type, const std::vector<uint8_t>& payload, MessageFlags flags = MessageFlags::NONE);
    void sendScreenFrame(const ScreenFrame& frame);
    void sendHeartbeat();
    void sendError(ErrorCode code, const std::string& message);
    void sendQualityStatus(const QualityStatusMessage& status);

    // Event processing
    void processEvents();

    // Configuration
    void setMaxConnections(int max) { m_maxConnections = max; }
    void setHeartbeatInterval(int ms) { m_heartbeatInterval = ms; }
    void setCompressionEnabled(bool enabled) { m_compressionEnabled = enabled; }
    void setEncryptionEnabled(bool enabled) { m_encryptionEnabled = enabled; }

    // Statistics
    uint64_t bytesSent() const { return m_bytesSent; }
    uint64_t bytesReceived() const { return m_bytesReceived; }
    int messagesSent() const { return m_messagesSent; }
    int messagesReceived() const { return m_messagesReceived; }
    void resetStatistics();

    // Callbacks
    void setClientConnectedCallback(std::function<void(const std::string&)> callback);
    void setClientDisconnectedCallback(std::function<void()> callback);
    void setMessageReceivedCallback(std::function<void(MessageType, const std::vector<uint8_t>&)> callback);
    void setErrorCallback(std::function<void(ErrorCode, const std::string&)> callback);
    void setQualityControlCallback(std::function<void(const QualityControlMessage&)> callback);

private:
    // Socket operations
    bool createServerSocket();
    void closeServerSocket();
    bool acceptConnection();
    void closeClientConnection();

    // Message processing
    void processIncomingData();
    bool parseMessage(const std::vector<uint8_t>& data, MessageHeader& header, std::vector<uint8_t>& payload);
    std::vector<uint8_t> createMessage(MessageType type, const std::vector<uint8_t>& payload, MessageFlags flags);

    // Data processing
    std::vector<uint8_t> compressData(const std::vector<uint8_t>& data);
    std::vector<uint8_t> decompressData(const std::vector<uint8_t>& data);
    std::vector<uint8_t> encryptData(const std::vector<uint8_t>& data);
    std::vector<uint8_t> decryptData(const std::vector<uint8_t>& data);

    // Network I/O
    int sendData(const void* data, int size);
    int receiveData(void* buffer, int size);

    // Connection validation
    bool validateClient();
    bool performHandshake();

    // Server socket
    SOCKET m_serverSocket;
    SOCKET m_clientSocket;
    int m_port;

    // Configuration
    int m_maxConnections;
    int m_heartbeatInterval;
    bool m_compressionEnabled;
    bool m_encryptionEnabled;

    // Message handling
    std::vector<uint8_t> m_receiveBuffer;
    std::queue<std::vector<uint8_t>> m_sendQueue;
    uint32_t m_nextSequenceNumber;
    uint32_t m_expectedSequenceNumber;

    // Statistics
    uint64_t m_bytesSent;
    uint64_t m_bytesReceived;
    int m_messagesSent;
    int m_messagesReceived;

    // Connection state
    std::string m_clientAddress;
    bool m_authenticated;
    std::chrono::steady_clock::time_point m_connectionTime;
    std::chrono::steady_clock::time_point m_lastHeartbeat;

    // Security
    std::vector<uint8_t> m_encryptionKey;
    std::vector<uint8_t> m_hmacKey;

    // Callbacks
    std::function<void(const std::string&)> m_clientConnectedCallback;
    std::function<void()> m_clientDisconnectedCallback;
    std::function<void(MessageType, const std::vector<uint8_t>&)> m_messageReceivedCallback;
    std::function<void(ErrorCode, const std::string&)> m_errorCallback;
    std::function<void(const QualityControlMessage&)> m_qualityControlCallback;

    // Threading
    std::atomic<bool> m_running;
    CRITICAL_SECTION m_sendCriticalSection;

    // Constants
    static constexpr int MAX_MESSAGE_QUEUE_SIZE = 1000;
    static constexpr int RECEIVE_BUFFER_SIZE = 1024 * 1024;  // 1MB
    static constexpr int HEARTBEAT_TIMEOUT = 60000;  // 60 seconds
};
