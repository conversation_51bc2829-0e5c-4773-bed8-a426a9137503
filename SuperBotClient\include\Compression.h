#pragma once

#include "Common.h"
#include <vector>
#include <memory>

namespace Compression
{
    // Compression types
    enum class CompressionType : uint8_t
    {
        None = 0,
        RLE = 1,        // Run-Length Encoding (simple, fast)
        LZ77 = 2,       // Simple LZ77 implementation
        Windows = 3,    // Windows built-in compression
        LZ4 = 4         // LZ4 compression
    };

    // Compression result
    struct CompressionResult
    {
        std::vector<uint8_t> data;
        CompressionType type;
        size_t originalSize;
        size_t compressedSize;
        double ratio;
        bool success;
    };

    // Compression interface
    class ICompressor
    {
    public:
        virtual ~ICompressor() = default;
        virtual CompressionResult compress(const std::vector<uint8_t>& data) = 0;
        virtual std::vector<uint8_t> decompress(const std::vector<uint8_t>& compressedData, size_t originalSize) = 0;
        virtual CompressionType getType() const = 0;
    };

    // Run-Length Encoding compressor (good for screen data with repeated pixels)
    class RLECompressor : public ICompressor
    {
    public:
        CompressionResult compress(const std::vector<uint8_t>& data) override;
        std::vector<uint8_t> decompress(const std::vector<uint8_t>& compressedData, size_t originalSize) override;
        CompressionType getType() const override { return CompressionType::RLE; }
    };

    // Simple LZ77 compressor
    class LZ77Compressor : public ICompressor
    {
    public:
        CompressionResult compress(const std::vector<uint8_t>& data) override;
        std::vector<uint8_t> decompress(const std::vector<uint8_t>& compressedData, size_t originalSize) override;
        CompressionType getType() const override { return CompressionType::LZ77; }

    private:
        struct Match
        {
            int distance;
            int length;
        };

        Match findLongestMatch(const std::vector<uint8_t>& data, size_t position, size_t windowSize = 4096);
    };

    // Windows built-in compression (using Compression API)
    class WindowsCompressor : public ICompressor
    {
    public:
        CompressionResult compress(const std::vector<uint8_t>& data) override;
        std::vector<uint8_t> decompress(const std::vector<uint8_t>& compressedData, size_t originalSize) override;
        CompressionType getType() const override { return CompressionType::Windows; }
    };

    // LZ4 compressor
    class LZ4Compressor : public ICompressor
    {
    public:
        CompressionResult compress(const std::vector<uint8_t>& data) override;
        std::vector<uint8_t> decompress(const std::vector<uint8_t>& compressedData, size_t originalSize) override;
        CompressionType getType() const override { return CompressionType::LZ4; }
    };

    // Compression manager - chooses best compressor based on data type
    class CompressionManager
    {
    public:
        CompressionManager();
        ~CompressionManager();

        // Compress data using the best available method
        CompressionResult compressScreenData(const std::vector<uint8_t>& data);
        
        // Decompress data
        std::vector<uint8_t> decompress(const std::vector<uint8_t>& compressedData, 
                                       CompressionType type, 
                                       size_t originalSize);

        // Set preferred compression type
        void setPreferredCompression(CompressionType type);
        
        // Get compression statistics
        struct Stats
        {
            size_t totalBytesCompressed;
            size_t totalBytesDecompressed;
            size_t totalCompressionOperations;
            size_t totalDecompressionOperations;
            double averageCompressionRatio;
            double totalCompressionTime;
            double totalDecompressionTime;
        };
        
        Stats getStatistics() const { return m_stats; }
        void resetStatistics();

    private:
        std::unique_ptr<RLECompressor> m_rleCompressor;
        std::unique_ptr<LZ77Compressor> m_lz77Compressor;
        std::unique_ptr<WindowsCompressor> m_windowsCompressor;
        std::unique_ptr<LZ4Compressor> m_lz4Compressor;
        
        CompressionType m_preferredType;
        Stats m_stats;
        
        // Choose best compressor for given data
        ICompressor* chooseBestCompressor(const std::vector<uint8_t>& data);
        
        // Update statistics
        void updateCompressionStats(const CompressionResult& result, double timeMs);
        void updateDecompressionStats(size_t originalSize, size_t compressedSize, double timeMs);
    };

    // Utility functions
    namespace Utils
    {
        // Calculate compression ratio
        double calculateCompressionRatio(size_t originalSize, size_t compressedSize);
        
        // Estimate compression benefit for screen data
        bool shouldCompress(const std::vector<uint8_t>& data, size_t threshold = 1024);
        
        // Analyze data characteristics
        struct DataAnalysis
        {
            double entropy;
            size_t uniqueBytes;
            size_t longestRun;
            bool hasRepeatingPatterns;
        };
        
        DataAnalysis analyzeData(const std::vector<uint8_t>& data);
    }
}
