﻿#pragma once

#include "Common.h"

class SecurityManager
{
public:
    SecurityManager();
    ~SecurityManager();

    // Initialization
    bool initialize();
    void shutdown();

    // Encryption/Decryption
    std::vector<uint8_t> encrypt(const std::vector<uint8_t>& data, const std::vector<uint8_t>& key);
    std::vector<uint8_t> decrypt(const std::vector<uint8_t>& encryptedData, const std::vector<uint8_t>& key);

    // Key generation
    std::vector<uint8_t> generateAESKey();
    std::vector<uint8_t> generateHMACKey();
    std::pair<std::vector<uint8_t>, std::vector<uint8_t>> generateRSAKeyPair();

    // Hashing
    std::vector<uint8_t> computeHash(const std::vector<uint8_t>& data, const std::string& algorithm = "SHA256");
    bool verifyHash(const std::vector<uint8_t>& data, const std::vector<uint8_t>& hash, const std::string& algorithm = "SHA256");

    // Digital signatures
    std::vector<uint8_t> signData(const std::vector<uint8_t>& data, const std::vector<uint8_t>& privateKey);
    bool verifySignature(const std::vector<uint8_t>& data, const std::vector<uint8_t>& signature, const std::vector<uint8_t>& publicKey);

    // Authentication tokens
    std::string generateAuthToken(const std::string& clientId, int expirySeconds);
    bool validateAuthToken(const std::string& token);
    std::string extractClientIdFromToken(const std::string& token);

    // Certificate management
    bool loadCertificate(const std::string& certificatePath, const std::string& password = "");
    bool validateCertificate(const std::vector<uint8_t>& certificateData);
    std::vector<uint8_t> generateSelfSignedCertificate(const std::string& subjectName);

    // Configuration
    void setEncryptionAlgorithm(const std::string& algorithm) { m_encryptionAlgorithm = algorithm; }
    void setKeySize(int keySize) { m_keySize = keySize; }
    void setEncryptionEnabled(bool enabled) { m_encryptionEnabled = enabled; }
    void setRequireClientCertificate(bool required) { m_requireClientCertificate = required; }
    void setAllowSelfSignedCertificates(bool allow) { m_allowSelfSignedCertificates = allow; }

    // State
    bool isEncryptionEnabled() const { return m_encryptionEnabled; }
    bool requiresClientCertificate() const { return m_requireClientCertificate; }
    bool allowsSelfSignedCertificates() const { return m_allowSelfSignedCertificates; }

private:
    // Windows CryptoAPI helpers
    bool initializeCryptoAPI();
    void cleanupCryptoAPI();
    
    // Encryption helpers
    std::vector<uint8_t> aesEncrypt(const std::vector<uint8_t>& data, const std::vector<uint8_t>& key);
    std::vector<uint8_t> aesDecrypt(const std::vector<uint8_t>& encryptedData, const std::vector<uint8_t>& key);
    
    // Hash helpers
    std::vector<uint8_t> sha256Hash(const std::vector<uint8_t>& data);
    std::vector<uint8_t> sha1Hash(const std::vector<uint8_t>& data);
    
    // Random number generation
    std::vector<uint8_t> generateRandomBytes(size_t size);
    
    // Base64 encoding/decoding
    std::string base64Encode(const std::vector<uint8_t>& data);
    std::vector<uint8_t> base64Decode(const std::string& encoded);

    // Configuration
    std::string m_encryptionAlgorithm;
    int m_keySize;
    bool m_encryptionEnabled;
    bool m_requireClientCertificate;
    bool m_allowSelfSignedCertificates;

    // Windows CryptoAPI handles
    HCRYPTPROV m_cryptProvider;
    HCRYPTKEY m_aesKey;
    
    // Certificate store
    HCERTSTORE m_certificateStore;
    PCCERT_CONTEXT m_clientCertificate;
    
    // State
    bool m_initialized;
};
